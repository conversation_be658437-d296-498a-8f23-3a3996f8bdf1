#include "testCaseManager.h"
#include <iostream>
#include <chrono>
#include "commonFunction.h"
#include <dirent.h>
#include <cstring> // for strcmp
#include <vector>
#include <string>
#include <filesystem>
#include <algorithm>
#include <fstream>
#include <json/json.h>
#include <thread>
#include <chrono>
#include <memory>


CTestCaseManager::CTestCaseManager(CMediator* pMediator)
    : CModuleBase(pMediator)
    , m_pUdpPusher(nullptr)
    , m_pMqttPusher(nullptr)
{
    std::cout << "CTestCaseManager构造函数" << std::endl;
    m_strCompressFilePath = gCompressFilePath;
    m_strTestCaseFilePath = gTestCaseFilePath;
    m_strTestCaseResultFilePath = gTestCaseResultFilePath;

}

CTestCaseManager::~CTestCaseManager()
{
    Stop();
    StopFilePushThreads(); // 确保停止所有文件推送线程
    std::cout << "CTestCaseManager析构函数" << std::endl;
}

void CTestCaseManager::Init()
{
    std::cout << "CTestCaseManager初始化开始" << std::endl;

    // 初始化数据推送器
    if (!InitDataPushers())
    {
        std::cout << "初始化数据推送器失败" << std::endl;
        return;
    }

    std::cout << "CTestCaseManager初始化完成" << std::endl;
    loadLocalTestCase();
}

void CTestCaseManager::Start()
{
    std::cout << "CTestCaseManager启动开始" << std::endl;
    
    // 启动数据推送器
    if (!StartDataPushers())
    {
        std::cout << "启动数据推送器失败" << std::endl;
        return;
    }

    std::cout << "CTestCaseManager启动完成" << std::endl;
}

void CTestCaseManager::Stop()
{
    std::cout << "CTestCaseManager停止开始" << std::endl;

    // 停止数据推送器
    StopDataPushers();

    std::cout << "CTestCaseManager停止完成" << std::endl;
}

bool CTestCaseManager::MsgFilter(u32 msgType)
{   
    bool ret = true;
    if (msgType == SYSTEM_INIT ||
        msgType == SYSTEM_START ||
        msgType == SYSTEM_STOP ||
        msgType == SYSTEM_PAUSE ||
        msgType == TESTCASE_GET_ALL_INFO ||
        msgType == TESTCASE_GET_UPLOAD_FILE_LIST_REQ ||
        msgType == TESTCASE_ADD_TESTCASE_REQ ||
        msgType == TESTCASE_CONTROL_DATA_PUSH_REQ ||
        msgType == TESTCASE_DELETE_ONE_REQ)
    {
        ret = false;
    }
    return ret;
}

void CTestCaseManager::HandleMsg(u32 msgLevel, u32 deviceId, u32 msgType, std::shared_ptr<void> spData)
{
    switch (msgType)
    {
    case SYSTEM_INIT:
        Init();
        break;
    case SYSTEM_START:
        Start();
        break;
    case SYSTEM_STOP:
        // Stop();
        break;
    case SYSTEM_PAUSE:
        Pause();
        break;
    case TESTCASE_GET_ALL_INFO:
        hendleGetAllInfo(msgLevel, deviceId, spData);
        break;
    case TESTCASE_GET_UPLOAD_FILE_LIST_REQ:
        handleGetUploadFileListReq(msgLevel, deviceId, spData);
        break;
    case TESTCASE_ADD_TESTCASE_REQ:
        handleAddTestCaseReq(msgLevel, deviceId, spData);
        break;
    case TESTCASE_CONTROL_DATA_PUSH_REQ:
        handleControlDataPushReq(msgLevel, deviceId, spData);
        break;
    case TESTCASE_DELETE_ONE_REQ:
        handleTestCaseDeleteOne(msgLevel, deviceId, spData);
        break;
    default:
        break;
    };
}

void CTestCaseManager::Pause()
{
    std::cout << "CTestCaseManager暂停" << std::endl;

    std::lock_guard<std::mutex> lock(m_pusherMutex);

    // 暂停UDP推送器
    if (m_pUdpPusher)
    {
        m_pUdpPusher->Pause();
    }

    // 暂停MQTT推送器
    if (m_pMqttPusher)
    {
        m_pMqttPusher->Pause();
    }
}

bool CTestCaseManager::Resume()
{
    std::cout << "CTestCaseManager恢复" << std::endl;

    std::lock_guard<std::mutex> lock(m_pusherMutex);

    // 恢复UDP推送器
    if (m_pUdpPusher)
    {
        m_pUdpPusher->Resume();
    }

    // 恢复MQTT推送器
    if (m_pMqttPusher)
    {
        m_pMqttPusher->Resume();
    }

    return true;
}

bool CTestCaseManager::InitDataPushers()
{
    std::lock_guard<std::mutex> lock(m_pusherMutex);

    try
    {
        // 创建UDP推送器
        if (m_udpConfig.bEnable)
        {
            m_pUdpPusher = std::make_shared<CUdpDataPusher>();
            if (!m_pUdpPusher)
            {
                std::cout << "创建UDP推送器失败" << std::endl;
                return false;
            }

            // 设置状态回调
            m_pUdpPusher->SetStatusCallback(
                [this](EPusherStatus status, EPusherError error, const std::string& errorMsg) {
                    OnUdpPusherStatus(status, error, errorMsg);
                }
            );

            // 初始化UDP推送器
            TUdpPusherConfig udpConfig = ConvertUdpConfig(m_udpConfig);
            if (!m_pUdpPusher->Init(udpConfig))
            {
                std::cout << "初始化UDP推送器失败" << std::endl;
                m_pUdpPusher.reset();
                return false;
            }

            std::cout << "UDP推送器初始化成功" << std::endl;
        }

        // 创建MQTT推送器
        if (m_mqttConfig.bEnable)
        {
            m_pMqttPusher = std::make_shared<CMqttDataPusher>();
            if (!m_pMqttPusher)
            {
                std::cout << "创建MQTT推送器失败" << std::endl;
                return false;
            }

            // 设置状态回调
            m_pMqttPusher->SetStatusCallback(
                [this](EPusherStatus status, EPusherError error, const std::string& errorMsg) {
                    OnMqttPusherStatus(status, error, errorMsg);
                }
            );

            // 初始化MQTT推送器
            TMqttPusherConfig mqttConfig = ConvertMqttConfig(m_mqttConfig);
            if (!m_pMqttPusher->Init(mqttConfig))
            {
                std::cout << "初始化MQTT推送器失败" << std::endl;
                m_pMqttPusher.reset();
                return false;
            }

            std::cout << "MQTT推送器初始化成功" << std::endl;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        std::cout << "初始化数据推送器异常: " << e.what() << std::endl;
        return false;
    }
}

bool CTestCaseManager::StartDataPushers()
{
    std::lock_guard<std::mutex> lock(m_pusherMutex);

    try
    {
        // 启动UDP推送器
        if (m_pUdpPusher)
        {
            if (!m_pUdpPusher->Start())
            {
                std::cout << "启动UDP推送器失败" << std::endl;
                return false;
            }
            std::cout << "UDP推送器启动成功" << std::endl;
        }

        // 启动MQTT推送器
        if (m_pMqttPusher)
        {
            if (!m_pMqttPusher->Start())
            {
                std::cout << "启动MQTT推送器失败" << std::endl;
                return false;
            }
            std::cout << "MQTT推送器启动成功" << std::endl;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        std::cout << "启动数据推送器异常: " << e.what() << std::endl;
        return false;
    }
}

bool CTestCaseManager::StopDataPushers()
{
    std::lock_guard<std::mutex> lock(m_pusherMutex);

    try
    {
        // 停止UDP推送器
        if (m_pUdpPusher)
        {
            m_pUdpPusher->Stop();
            m_pUdpPusher.reset();
            std::cout << "UDP推送器已停止" << std::endl;
        }

        // 停止MQTT推送器
        if (m_pMqttPusher)
        {
            m_pMqttPusher->Stop();
            m_pMqttPusher.reset();
            std::cout << "MQTT推送器已停止" << std::endl;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        std::cout << "停止数据推送器异常: " << e.what() << std::endl;
        return false;
    }
}

bool CTestCaseManager::PushData(const std::string& data)
{
    std::lock_guard<std::mutex> lock(m_pusherMutex);

    bool result = true;

    try
    {
        // 推送到UDP
        if (m_pUdpPusher && m_udpConfig.bEnable)
        {
            if (!m_pUdpPusher->PushData(data))
            {
                std::cout << "UDP推送数据失败" << std::endl;
                result = false;
            }
        }

        // 推送到MQTT
        if (m_pMqttPusher && m_mqttConfig.bEnable)
        {
            if (!m_pMqttPusher->PushData(data))
            {
                std::cout << "MQTT推送数据失败" << std::endl;
                result = false;
            }
        }

        return result;
    }
    catch (const std::exception& e)
    {
        std::cout << "推送数据异常: " << e.what() << std::endl;
        return false;
    }
}

void CTestCaseManager::SetUdpPusherConfig(const TUdpPusherManagerConfig& config)
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_udpConfig = config;
    std::cout << "UDP推送器配置已更新" << std::endl;
}

void CTestCaseManager::SetMqttPusherConfig(const TMqttPusherManagerConfig& config)
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_mqttConfig = config;
    std::cout << "MQTT推送器配置已更新" << std::endl;
}

TUdpPusherManagerConfig CTestCaseManager::GetUdpPusherConfig() const
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_udpConfig;
}

TMqttPusherManagerConfig CTestCaseManager::GetMqttPusherConfig() const
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_mqttConfig;
}

void CTestCaseManager::SetUdpPusherStatusCallback(const TPusherStatusCallback& callback)
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_udpStatusCallback = callback;
}

void CTestCaseManager::SetMqttPusherStatusCallback(const TPusherStatusCallback& callback)
{
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_mqttStatusCallback = callback;
}

CDataPusherBase::Statistics CTestCaseManager::GetUdpPusherStatistics() const
{
    std::lock_guard<std::mutex> lock(m_pusherMutex);
    if (m_pUdpPusher)
    {
        return m_pUdpPusher->GetStatistics();
    }
    return CDataPusherBase::Statistics{};
}

CDataPusherBase::Statistics CTestCaseManager::GetMqttPusherStatistics() const
{
    std::lock_guard<std::mutex> lock(m_pusherMutex);
    if (m_pMqttPusher)
    {
        return m_pMqttPusher->GetStatistics();
    }
    return CDataPusherBase::Statistics{};
}

EPusherStatus CTestCaseManager::GetUdpPusherStatus() const
{
    std::lock_guard<std::mutex> lock(m_pusherMutex);
    if (m_pUdpPusher)
    {
        return m_pUdpPusher->GetStatus();
    }
    return EPusherStatus::DISCONNECTED;
}

EPusherStatus CTestCaseManager::GetMqttPusherStatus() const
{
    std::lock_guard<std::mutex> lock(m_pusherMutex);
    if (m_pMqttPusher)
    {
        return m_pMqttPusher->GetStatus();
    }
    return EPusherStatus::DISCONNECTED;
}

void CTestCaseManager::OnUdpPusherStatus(EPusherStatus status, EPusherError error, const std::string& errorMsg)
{
    std::cout << "UDP推送器状态变化: 状态=" << static_cast<int>(status)
              << ", 错误=" << static_cast<int>(error) << ", 消息=" << errorMsg << std::endl;

    // 调用外部回调
    if (m_udpStatusCallback)
    {
        m_udpStatusCallback(status, error, errorMsg);
    }
}

void CTestCaseManager::OnMqttPusherStatus(EPusherStatus status, EPusherError error, const std::string& errorMsg)
{
    std::cout << "MQTT推送器状态变化: 状态=" << static_cast<int>(status)
              << ", 错误=" << static_cast<int>(error) << ", 消息=" << errorMsg << std::endl;

    // 调用外部回调
    if (m_mqttStatusCallback)
    {
        m_mqttStatusCallback(status, error, errorMsg);
    }
}

TUdpPusherConfig CTestCaseManager::ConvertUdpConfig(const TUdpPusherManagerConfig& config) const
{
    TUdpPusherConfig udpConfig;
    udpConfig.bEnable = config.bEnable;
    udpConfig.strAddr = config.strAddr;
    udpConfig.dwPort = config.dwPort;
    udpConfig.dwTimeoutMs = config.dwTimeoutMs;
    udpConfig.dwRetryCount = config.dwRetryCount;
    udpConfig.dwRetryIntervalMs = config.dwRetryIntervalMs;
    udpConfig.bAutoReconnect = config.bAutoReconnect;
    udpConfig.bBroadcast = config.bBroadcast;
    udpConfig.dwMaxPacketSize = config.dwMaxPacketSize;
    return udpConfig;
}

TMqttPusherConfig CTestCaseManager::ConvertMqttConfig(const TMqttPusherManagerConfig& config) const
{
    TMqttPusherConfig mqttConfig;
    mqttConfig.bEnable = config.bEnable;
    mqttConfig.strAddr = config.strAddr;
    mqttConfig.dwPort = config.dwPort;
    mqttConfig.dwTimeoutMs = config.dwTimeoutMs;
    mqttConfig.dwRetryCount = config.dwRetryCount;
    mqttConfig.dwRetryIntervalMs = config.dwRetryIntervalMs;
    mqttConfig.bAutoReconnect = config.bAutoReconnect;
    mqttConfig.strClientId = config.strClientId;
    mqttConfig.strTopic = config.strTopic;
    mqttConfig.strUsername = config.strUsername;
    mqttConfig.strPassword = config.strPassword;
    mqttConfig.dwQos = config.dwQos;
    mqttConfig.bRetain = config.bRetain;
    mqttConfig.bCleanSession = config.bCleanSession;
    mqttConfig.dwKeepAliveSeconds = config.dwKeepAliveSeconds;
    mqttConfig.bUseTls = config.bUseTls;
    return mqttConfig;
}

bool CTestCaseManager::loadLocalTestCase()
{
    
    // 检查根目录是否存在
    if (!commonFunc::isDirectory(m_strTestCaseFilePath)) {
        throw std::runtime_error("Invalid root path: " + m_strTestCaseFilePath);
        return false;
    }
    m_vecTestCaseInfoList.clear();
    // JSON解析器设置
    Json::CharReaderBuilder readerBuilder;
    Json::Value root;
    std::string errs;

    // 遍历根目录下的所有子目录
    for (const auto& dir_name : commonFunc::listDirectory(m_strTestCaseFilePath)) {
        std::string dir_path = m_strTestCaseFilePath + "/" + dir_name;
        if (!commonFunc::isDirectory(dir_path)) continue;
        
        std::string task_path = dir_path + "/task.json";
        
        // 检查task.json是否存在
        if (!commonFunc::isRegularFile(task_path)) continue;
        
        try {

            // 获取文件夹创建时间戳（13位UTC）
            int64_t create_ts = commonFunc::getCreationTimestamp(dir_path);
            // 读取JSON文件
            std::ifstream ifs(task_path);
            if (!ifs.is_open()) {
                throw std::runtime_error("Failed to open: " + task_path);
            }

            // 解析JSON
            if (!Json::parseFromStream(readerBuilder, ifs, &root, &errs)) {
                throw std::runtime_error("JSON parse error: " + errs);
            }

            // 检查是否是数组且至少有一个元素
            if (!root.isArray() || root.size() == 0) {
                throw std::runtime_error("Invalid task.json format: expected non-empty array");
            }

            // 获取第一个(也是唯一一个)测试任务
            const Json::Value& task = root[0];
            TestCase::TestTask test_task;
            test_task.folder_path = dir_path;
            test_task.test_name = task["test_name"].asString();
            test_task.create_timestamp = create_ts; // 设置13位时间戳
            test_task.data_uid = commonFunc::generateUniqueRandomString(8);
            // 解析文件列表
            const Json::Value& data = task["data"];
            if (!data.isArray()) {
                throw std::runtime_error("Invalid data format: expected array");
            }

            for (const Json::Value& file_info : data) {
                std::string file_name = file_info["file_name"].asString();
                int freq = file_info["freq"].asInt();
                
                // 检查文件是否存在(不区分大小写)
                bool file_exists = false;
                std::string file_type ="";
                for (const auto& entry_name : commonFunc::listDirectory(dir_path)) {
                    std::string entry_path = dir_path + "/" + entry_name;
                    if (!commonFunc::isRegularFile(entry_path)) continue;
                    
                    std::string entry_base = entry_name.substr(0, entry_name.find_last_of('.'));
                    std::string expected_base = file_name.substr(0, file_name.find_last_of('.'));
                    
                    // 统一转换为大写比较
                    std::transform(entry_base.begin(), entry_base.end(), entry_base.begin(), ::toupper);
                    std::transform(expected_base.begin(), expected_base.end(), expected_base.begin(), ::toupper);
                    //rsu 设备和obu设备区分
                    if(gSoftwareVersion == SOFTWARE_VERSION_RSU)
                    {
                        if (entry_base == "RSM" || entry_base == "MAP" || entry_base == "SPAT" || entry_base == "RSI" || entry_base == "RTE" || entry_base == "RTS")
                        {
                            file_exists = true;
                            file_name = entry_name; // 使用实际文件名
                            file_type = entry_base;
                            break;
                        }
                    }else if(gSoftwareVersion == SOFTWARE_VERSION_OBU)
                    {
                        if (entry_base == "BSM" )
                        {
                            file_exists = true;
                            file_name = entry_name; // 使用实际文件名
                            file_type = entry_base;
                            break;
                        }
                    }
                    // if (entry_base == expected_base) {
                    //     file_exists = true;
                    //     file_name = entry_name; // 使用实际文件名
                    //     file_type = entry_base;
                    //     break;
                    // }
                }
                
                if (file_exists) {
                    test_task.addFile(file_name, freq);
                    test_task.file_type += file_type+" ";
                } else {
                    std::cerr << "Warning: File " << file_name << " not found in " 
                              << dir_path << std::endl;
                }
            }
            if(test_task.files.size()>0)
            {
                m_vecTestCaseInfoList.push_back(std::move(test_task));
            }
            
        } catch (const std::exception& e) {
            std::cerr << "Error parsing " << task_path << ": " << e.what() << std::endl;
            continue;
        }
    }
    return true;
}

bool CTestCaseManager::loadCompressFile()
{
    // 读取目录下所有文件
    DIR* dir = opendir(m_strCompressFilePath.c_str());
    if (dir == nullptr) {
        // 无法打开目录
        return false;
    }
    m_vecCompressFileList.clear();
    struct dirent* entry;
    while ((entry = readdir(dir)) != nullptr) {
        // 跳过 "." 和 ".." 目录
        if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0) {
            continue;
        }

        // 检查文件名是否以.zip结尾
        std::string filename(entry->d_name);
        if (filename.size() >= 4 && 
            filename.compare(filename.size() - 4, 4, ".zip") == 0) {
                TTestCaseCompressFileInfo stFileInfo;
                stFileInfo.strFileName = filename;
                stFileInfo.fileUID = commonFunc::generateUniqueRandomString(8);
                m_vecCompressFileList.push_back(stFileInfo);
        }
    }
    closedir(dir);
    return true;
}

bool CTestCaseManager::handleAddTestCaseReq(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
    bool ret = false;
    std::shared_ptr<TAddTestCaseRes> spAddTestCaseRes = std::make_shared<TAddTestCaseRes>();

    do
    {
        std::shared_ptr<TTestCaseCompressFileInfo> spAddTestCasePackage = std::static_pointer_cast<TTestCaseCompressFileInfo>(spData);
        if (spAddTestCasePackage != nullptr)
        {
            // 组装压缩文件路径
            std::string compressFilePath = m_strCompressFilePath + spAddTestCasePackage->strFileName;
            // 检查文件是否存在
            if (!commonFunc::isFileExist(compressFilePath))
            {
                spAddTestCaseRes->isSucceed = false;
                spAddTestCaseRes->strErrMsg = "文件不存在";
                break; 
            }
            // 解压
            if (!commonFunc::unzipFile(compressFilePath, m_strTestCaseFilePath))
            {
                spAddTestCaseRes->isSucceed = false;
                spAddTestCaseRes->strErrMsg = "解压失败";
                break;
            }
            spAddTestCaseRes->strFileName = spAddTestCasePackage->strFileName;
            spAddTestCaseRes->fileUID = spAddTestCasePackage->fileUID;
            spAddTestCaseRes->isSucceed = true;
            spAddTestCaseRes->strErrMsg = "";

            //　更新测试用例
            if(!loadLocalTestCase())
            {
                spAddTestCaseRes->isSucceed = false;
                spAddTestCaseRes->strErrMsg = "读取本地文件失败";
                break;
            }

            // 发送结果到前端
            std::shared_ptr<TTestCaseInfoList> spTestCaseInfoList = std::make_shared<TTestCaseInfoList>();
            spTestCaseInfoList->isSucceed = true;
            // 遍历m_vecTestCaseList
            for (auto iter : m_vecTestCaseInfoList)
            {
                TTestCaseInfo stCaseInfo{};
                stCaseInfo.strCaseName = iter.test_name;
                stCaseInfo.llImportTime = iter.create_timestamp;
                stCaseInfo.strDataUID = iter.data_uid;
                stCaseInfo.strType = iter.file_type;
                spTestCaseInfoList->vecList.push_back(stCaseInfo);
            }
            Notify(MSG_LEVEL_ALL_DEVICE, NO_DEVICE, TESTCASE_UPDATE_INFO, std::static_pointer_cast<void>(spTestCaseInfoList));
            spAddTestCaseRes->isSucceed = true;
            ret = true;
        }
    } while (0);
    Notify(msgLevel, deviceId, TESTCASE_ADD_TESTCASE_RES, std::static_pointer_cast<void>(spAddTestCaseRes));
    return ret;
}

bool CTestCaseManager::handleControlDataPushReq(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
    bool ret = false;
    std::shared_ptr<TTestCasePushControl> spTestPushControl = std::static_pointer_cast<TTestCasePushControl>(spData);
    std::shared_ptr<TTestCaseCommonRes> spTestPushControlRes = std::make_shared<TTestCaseCommonRes>();
    do
    {
         // 推送
    if(spTestPushControl->emStatus ==TTestCasePushControl::STATR)
    {
        if (!m_bIsPusherInited)
        {
            std::cout << spTestPushControl->stConf.strAddress << std::endl;
            size_t colonPos = spTestPushControl->stConf.strAddress.find(':');
            if (colonPos == std::string::npos)
            {
                ERROR("this is no ip and port");
                spTestPushControlRes->strErrMsg = "this is no ip and port";
                break;
            }

            std::string ip = spTestPushControl->stConf.strAddress.substr(0, colonPos);
            std::string port = spTestPushControl->stConf.strAddress.substr(colonPos + 1);
            // 读取配置文件
            if (spTestPushControl->stConf.ePushWay == TTestCasePushConf::MQTT)
            {
                // MQTT
                m_mqttConfig.bEnable = true;
                m_mqttConfig.strAddr = ip;
                m_mqttConfig.dwPort = atoi(port.c_str());
                m_mqttConfig.strClientId = spTestPushControl->stConf.strClientID;
                m_mqttConfig.strTopic = spTestPushControl->stConf.strTopic;
                m_mqttConfig.strUsername = spTestPushControl->stConf.strUsername;
                m_mqttConfig.strPassword = spTestPushControl->stConf.strPWD;
            }
            else if (spTestPushControl->stConf.ePushWay == TTestCasePushConf::UDP)
            {
                // UDP
                m_udpConfig.bEnable = true;
                m_udpConfig.strAddr = ip;
                m_udpConfig.dwPort = atoi(port.c_str());
            }
            else
            {
                break;
            }
            InitDataPushers();
            StartDataPushers();
            Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,DATA_DATABASE_TESTCASE_START,nullptr);
            m_bIsPusherInited = true;
        }

        // 遍历文件推送数据
        std::string targetDataUid = spTestPushControl->stConf.strDataUid;

        // 根据strDataUid找到对应的TestTask
        TestCase::TestTask* pTargetTask = nullptr;
        for (auto& task : m_vecTestCaseInfoList) {
            std::cout << "data_uid: " << task.data_uid << std::endl;
            std::cout << "targetDataUid: " << targetDataUid << std::endl;
            if (task.data_uid == targetDataUid) {
                pTargetTask = &task;
                break;
            }
        }

        if (pTargetTask == nullptr) {
            ERROR("未找到对应的测试任务，data_uid: " + targetDataUid);
            spTestPushControlRes->strErrMsg = "未找到对应的测试任务";
            break;
        }

        INFO("找到测试任务: " + pTargetTask->test_name + ", 包含 " + std::to_string(pTargetTask->files.size()) + " 个文件");

        // 停止之前的推送线程
        StopFilePushThreads();

        // 遍历文件，为每个文件创建推送线程
        for (const auto& fileInfo : pTargetTask->files) {
            std::string filePath = pTargetTask->folder_path + "/" + fileInfo.name;

            // 检查文件是否存在
            std::ifstream file(filePath);
            if (!file.is_open()) {
                ERROR("无法打开文件: " + filePath);
                spTestPushControlRes->strErrMsg = "无法打开文件: " + filePath;
                continue;
            }

            // 读取文件内容
            std::string fileContent((std::istreambuf_iterator<char>(file)),
                                   std::istreambuf_iterator<char>());
            file.close();

            if (fileContent.empty()) {
                ERROR("文件内容为空: " + filePath);
                spTestPushControlRes->strErrMsg = "文件内容为空: " + filePath;
                continue;
            }

            INFO("读取文件成功: " + filePath + ", 大小: " + std::to_string(fileContent.size()) + " 字节, 频率: " + std::to_string(fileInfo.freq) + "Hz");

            // 创建文件推送线程
            CreateFilePushThread(fileInfo.base_name,fileInfo.name, fileContent, fileInfo.freq);
        }

        INFO("已创建 " + std::to_string(m_filePushThreads.size()) + " 个文件推送线程");

    }
    else if (spTestPushControl->emStatus == TTestCasePushControl::PAUSE) // 暂停
    {
        // 暂停文件推送线程
        StopFilePushThreads();
        INFO("已暂停文件推送");
    }else if(spTestPushControl->emStatus == TTestCasePushControl::STOP)// 结束
    {
        // 停止文件推送线程
        StopFilePushThreads();

        if(m_bIsPusherInited)
        {
            StopDataPushers();
            Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,DATA_DATABASE_TESTCASE_STOP,nullptr);
            m_bIsPusherInited = false;
        }

        INFO("已停止文件推送");
    }
    ret=true;
    } while (0);   
    
    spTestPushControlRes->isSucceed = ret;
    Notify(msgLevel, deviceId, TESTCASE_CONTROL_DATA_PUSH_RES, std::static_pointer_cast<void>(spTestPushControlRes));
    return ret;
}

bool CTestCaseManager::hendleGetAllInfo(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{   
    
    std::shared_ptr<TTestCaseInfoList> spTestCaseInfoList = std::make_shared<TTestCaseInfoList>();
    spTestCaseInfoList->isSucceed = loadLocalTestCase();
    for (auto iter : m_vecTestCaseInfoList)
    {
        TTestCaseInfo stCaseInfo{};
        stCaseInfo.strCaseName = iter.test_name;
        stCaseInfo.llImportTime = iter.create_timestamp;
        stCaseInfo.strDataUID = iter.data_uid;
        stCaseInfo.strType = iter.file_type;
        spTestCaseInfoList->vecList.push_back(stCaseInfo);
    }
    Notify(msgLevel, deviceId, TESTCASE_UPDATE_INFO, std::static_pointer_cast<void>(spTestCaseInfoList));
    return true;
}

bool CTestCaseManager::handleGetUploadFileListReq(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
    std::cout  << __func__ << std::endl;
    bool ret = false;
    std::shared_ptr<TTestCaseCompressFileInfoList> spTestCaseCompressFileInfoList = std::make_shared<TTestCaseCompressFileInfoList>();
    do
    {
        // 　更新测试用例
        if (!loadCompressFile())
        {
            spTestCaseCompressFileInfoList->isSucceed = false;
            spTestCaseCompressFileInfoList->strErrMsg = "读取本地文件失败";
            break;
        }
        for(auto iter: m_vecCompressFileList)
        {
            spTestCaseCompressFileInfoList->vecList.push_back(iter);
        }
        spTestCaseCompressFileInfoList->isSucceed = true;
        ret = true;
    } while (0);
     Notify(msgLevel, deviceId, TESTCASE_GET_UPLOAD_FILE_LIST_RES, std::static_pointer_cast<void>(spTestCaseCompressFileInfoList));
    return ret;
}


bool CTestCaseManager::handleTestCaseDeleteOne(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData)
{
    std::cout  << __func__ << std::endl;
    bool ret = false;
    std::shared_ptr<TTestCaseCommonRes> spRes = std::make_shared<TTestCaseCommonRes>();
    do
    {
        std::shared_ptr<TTestCaseInfo> spTestCaseCompressFileInfoList = std::static_pointer_cast<TTestCaseInfo>(spData);
        if(spTestCaseCompressFileInfoList)
        {
            // 查找对应的文件路径并删除
            for(auto iter : m_vecTestCaseInfoList)
            {
                if(iter.data_uid == spTestCaseCompressFileInfoList->strDataUID)
                {
                    // 删除文件夹
                    commonFunc::deleteFolder(iter.folder_path);
                    break;
                }
            }
            if(!loadLocalTestCase())
            {
                break;
            }
            // 发送结果到前端
            std::shared_ptr<TTestCaseInfoList> spTestCaseInfoList = std::make_shared<TTestCaseInfoList>();
            spTestCaseInfoList->isSucceed = true;
            // 遍历m_vecTestCaseList
            for (auto iter : m_vecTestCaseInfoList)
            {
                TTestCaseInfo stCaseInfo{};
                stCaseInfo.strCaseName = iter.test_name;
                stCaseInfo.llImportTime = iter.create_timestamp;
                stCaseInfo.strDataUID = iter.data_uid;
                stCaseInfo.strType = iter.file_type;
                spTestCaseInfoList->vecList.push_back(stCaseInfo);
            }
            Notify(MSG_LEVEL_ALL_DEVICE, NO_DEVICE, TESTCASE_UPDATE_INFO, std::static_pointer_cast<void>(spTestCaseInfoList));
            ret = true;
        }
        
    } while (0);
    spRes->isSucceed = ret;
    Notify(msgLevel, deviceId, TESTCASE_DELETE_ONE_RES, std::static_pointer_cast<void>(spRes));
    return ret;
}

// 创建文件推送线程
void CTestCaseManager::CreateFilePushThread(const std::string& baseName,const std::string& fileName, const std::string& fileContent, int frequency)
{
    std::lock_guard<std::mutex> lock(m_filePushMutex);

    // 创建线程信息对象
    auto threadInfo = std::make_shared<TestCase::FilePushThread>(baseName,fileName, fileContent, frequency);

    // 创建并启动线程
    threadInfo->thread = std::thread(&CTestCaseManager::FilePushWorker, this, threadInfo);

    // 添加到线程列表
    m_filePushThreads.push_back(threadInfo);

    INFO("创建文件推送线程: " + fileName + ", 频率: " + std::to_string(frequency) + "Hz");
}

// 停止所有文件推送线程
void CTestCaseManager::StopFilePushThreads()
{
    std::lock_guard<std::mutex> lock(m_filePushMutex);

    INFO("正在停止 " + std::to_string(m_filePushThreads.size()) + " 个文件推送线程");

    // 设置停止标志
    for (auto& threadInfo : m_filePushThreads) {
        threadInfo->shouldStop = true;
    }

    // 等待所有线程结束
    for (auto& threadInfo : m_filePushThreads) {
        if (threadInfo->thread.joinable()) {
            threadInfo->thread.join();
        }
    }

    // 清空线程列表
    m_filePushThreads.clear();

    INFO("所有文件推送线程已停止");
}

void CTestCaseManager::FilePushWorker(std::shared_ptr<TestCase::FilePushThread> threadInfo)
{
    if (!threadInfo) {
        ERROR("线程信息为空");
        return;
    }

    INFO("文件推送线程启动: " + threadInfo->fileName + ", 频率: " + std::to_string(threadInfo->frequency) + "Hz");

    int intervalMs = (threadInfo->frequency > 0) ? (1000 / threadInfo->frequency) : 1000;
    int pushCount = 0;

    Json::Value jsonArray;
    Json::Reader reader;
    std::vector<Json::Value> parsedElements;

    if (!reader.parse(threadInfo->fileContent, jsonArray)) {
        ERROR("JSON解析失败: " + threadInfo->fileName + ", 错误: " + reader.getFormattedErrorMessages());
        return;
    }

    if (!jsonArray.isArray()) {
        ERROR("文件内容不是JSON数组: " + threadInfo->fileName);
        return;
    }

    for (const auto& element : jsonArray) {
        parsedElements.push_back(element);
    }

    INFO("文件 " + threadInfo->fileName + " 解析完成，包含 " + std::to_string(parsedElements.size()) + " 个数据元素");

    size_t currentIndex = 0;
    int msgCnt = 0;

    // 创建日志文件
    std::mutex logMutex;
    std::ofstream logFile;
    {
        // 生成文件名：原始文件名 + 时间戳
        auto t = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
        std::tm tm;
        localtime_r(&t, &tm);
        std::ostringstream oss;
        oss << m_strTestCaseResultFilePath+threadInfo->baseName << "_"
            << std::put_time(&tm, "%Y%m%d_%H%M%S") << ".txt";
        logFile.open(oss.str(), std::ios::out);
        if (!logFile.is_open()) {
            ERROR("无法创建日志文件: " + oss.str());
            return;
        }
    }

    Json::StreamWriterBuilder builder;
    builder["indentation"] = ""; // 紧凑格式
    builder["precision"] = 3;
    std::unique_ptr<Json::StreamWriter> writer(builder.newStreamWriter());

    while (!threadInfo->shouldStop) {
        try {
            if (currentIndex >= parsedElements.size()) {
                currentIndex = 0;
                // INFO("文件 " + threadInfo->fileName + " 所有元素发送完毕，重新开始循环");
            }

            // 当前帧数据
            Json::Value& frame = parsedElements[currentIndex];

            // 设置当前时间戳（毫秒）
            auto now = std::chrono::system_clock::now();
            auto timestampMs = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
            frame["timeStamp"] = static_cast<Json::Value::UInt64>(timestampMs);

            // 设置 msgCnt（外部与内部 value 中都设置）
            frame["msgCnt"] = msgCnt;
            if (frame.isMember("value") && frame["value"].isObject()) {
                frame["value"]["msgCnt"] = msgCnt;
            }

            // 提取 value 字段作为发送内容
            Json::Value valueToSend ;
            valueToSend["value"] = frame["value"];
            if(frame["value"].isMember("participants"))
            {
                valueToSend["type"] = "rsm"; 
            }else if(frame["value"].isMember("nodes"))
            {
                valueToSend["type"] = "map"; 
            }else if(frame["value"].isMember("intersections"))
            {
                valueToSend["type"] = "spat"; 
            }else if(frame["value"].isMember("rtes"))
            {
                valueToSend["type"] = "rte"; 
            }else if(frame["value"].isMember("rtss"))
            {
                valueToSend["type"] = "rts"; 
            }else if(frame["value"].isMember("secMark"))
            {
                valueToSend["type"] = "bsm"; 
            }else
            {
                valueToSend["type"] = "unkown"; 
            }
            std::ostringstream sendStream;
            writer->write(valueToSend, &sendStream);
            std::string sendStr = sendStream.str();

            // 执行推送
            if (PushData(sendStr)) {
                pushCount++;
                currentIndex++;
                msgCnt = (msgCnt + 1) % 128; // msgCnt循环

                // 写入完整帧数据到日志文件（加锁保护）
                {
                    std::lock_guard<std::mutex> lock(logMutex);
                    std::ostringstream fullStream;
                    writer->write(frame, &fullStream);
                    logFile << fullStream.str() << "\n";

                    std::string strRoadId = "recvdata";
                    if (gSoftwareVersion == SOFTWARE_VERSION_OBU)
                    {
                        strRoadId = "sendToOBU";
                    }
                    else if (gSoftwareVersion == SOFTWARE_VERSION_RSU)
                    {
                        strRoadId = "sendToRSU";
                    }
                    // 写入数据库
                    std::shared_ptr<TRawData> spData = std::make_shared<TRawData>();
                    spData->llDataTime = timestampMs;
                    spData->llRecvTime = timestampMs;
                    spData->strCrossroadId = strRoadId;
                    spData->strData = fullStream.str();
                    spData->wDataLength = fullStream.str().length();
                    Notify(MSG_LEVEL_ALL_DEVICE, NO_DEVICE, DATA_DATABASE_TESTCASE_DATA_UPDATE, std::static_pointer_cast<void>(spData));
                }
            } else {
                ERROR("推送数据失败: " + threadInfo->fileName + ", 元素索引: " + std::to_string(currentIndex));
                currentIndex++; // 即使失败也跳到下一个
                msgCnt = (msgCnt + 1) % 128;
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(intervalMs));

        } catch (const std::exception& e) {
            ERROR("文件推送线程异常: " + threadInfo->fileName + ", 错误: " + e.what());
            break;
        }
    }

    // 关闭日志文件
    {
        std::lock_guard<std::mutex> lock(logMutex);
        logFile.close();
    }

    INFO("文件推送线程结束: " + threadInfo->fileName + ", 总共推送 " + std::to_string(pushCount) + " 个元素");
}