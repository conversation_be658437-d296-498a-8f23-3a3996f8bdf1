#ifndef _MESSAGE_H_
#define _MESSAGE_H_
// 消息等级定义
#define MSG_LEVEL_ALL_DEVICE 0X01 // 全部设备
#define MSG_LEVEL_ONE_DEVICE 0X02 // 某一设备

// 系统状态
#define SYSTEM_STATUS_ONLINE 0X00 // 初始化
#define SYSTEM_STATUS_CAPTURING 0X01 // 开始


// 消息设备号定义
#define NO_DEVICE 0x00 // 无设备号

// 软件版本定义
#define SOFTWARE_VERSION_COMMON 0 // common
#define SOFTWARE_VERSION_RSU 1    // RSU
#define SOFTWARE_VERSION_OBU 2    // OBU

#define NULL_MESSAGE 0x00
/* 数据模块定义 */
#define MODULE_COMMUNICATION 0X01 // 交互模块
#define MODULE_DATA_CAPTURE 0X02  // 数据采集模块
#define MODULE_DATABASE 0X03      // 数据模块
#define MODULE_FILE_MANAGER 0X04  // 文件管理模块

/* server端内部消息定义*/
#define SYSTEM_INIT 0X1001             // 系统的初始化
#define SYSTEM_START 0X1002            // 系统开始
#define SYSTEM_PAUSE 0X1003            // 系统暂停
#define SYSTEM_STOP 0X1004             // 系统停止
#define SYSTEM_CLIENT_CONNECTED 0X1005 // 客户端连接成功
#define SYSTEM_UPDATE_STATUS 0X1006    // 系统状态更新
#define SYSTEM_INFO_REQ 0X1007         // 系统信息请求
#define SYSTEM_INFO_RES 0X1008         // 系统信息请求回应
#define CLIENT_INFO_REQ 0X1009         // 客户端信息请求
#define CLIENT_INFO_RES 0X1010         // 客户端信息请求回应
#define SYSTEM_NET_SET_REQ 0X1011      // 系统网络设置请求
#define SYSTEM_NET_SET_RES 0X1012      // 系统网络设置回应
#define SYSTEM_STORE_SET_REQ 0X1013    // 系统存储设置请求
#define SYSTEM_STORE_SET_RES 0X1014    // 系统存储设置回应
#define SYSTEM_CMD_REQ 0x1015          // 系统控制请求
#define SYSTEM_CMD_RES 0X1016          // 系统控制回应
#define SYSTEM_UPDATE_STORE_PATH 0X1017 //存储路径更新
#define SYSTEM_LOG_UPDATE 0X1018        //系统日志更新


#define CAPTURE_DATA_UPDATE 0X2001    // 数据更新
#define CAPTURE_STATUS_UPDATE 0X2002  // 采集状态更新(已弃用)
#define CAPTURE_FREQ_UPLOAD 0X2003    // 采集频率上报(已弃用)
#define CAPTURE_CONFIG_UPDATE 0X2004  // 参数配置更新
#define CAPTURE_MONITOR_UPDATE 0X2005 // 采集状态监控
#define CAPTURE_CONFIG_REQ 0X2006     // 参数配置请求
#define CAPTURE_CONFIG_RES 0X2007     // 参数配置响应

#define DATA_QUERY_REQ 0X3001          // 数据查询请求
#define DATA_QUERY_RES 0X3002          // 数据查询响应
#define DATA_COMPRESS_REQ 0X3003       // 数据压缩请求
#define DATA_COMPRESS_RES 0X3004       // 数据压缩响应
#define DATA_COMPRESS_QUERY_REQ 0X3005 // 压缩数据查询请求
#define DATA_COMPRESS_QUERY_RES 0X3006 // 压缩数据查询响应
#define DATA_ROADINFO_UPDATE 0X3007    // 路口信息更新
#define DATA_QUERY_BATCH_REQ 0X3008    // 分块数据查询请求
#define DATA_QUERY_BATCH_RES 0X3009    // 分块数据查询响应
#define DATA_DATABSE_CLOSED 0X3010     // 数据库关闭

#define DATA_DATABASE_TESTCASE_START 0X3021 // 测试用例数据开始
#define DATA_DATABASE_TESTCASE_STOP 0X3022 // 测试用例数据结束
#define DATA_DATABASE_TESTCASE_DATA_UPDATE 0X3023 // 测试用例数据更新

#define TESTCASE_GET_ALL_INFO 0x4001   // 获取全部测试用例信息
#define TESTCASE_UPDATE_INFO  0x4002   // 更新测试用例信息
#define TESTCASE_GET_UPLOAD_FILE_LIST_REQ 0x4003 //获取上传文件列表请求
#define TESTCASE_GET_UPLOAD_FILE_LIST_RES 0x4004 //获取上传文件列表响应
#define TESTCASE_ADD_TESTCASE_REQ 0x4005 //添加测试用例请求
#define TESTCASE_ADD_TESTCASE_RES 0x4006 //添加测试用例响应
#define TESTCASE_CONTROL_DATA_PUSH_REQ 0x4007 //控制数据推送请求
#define TESTCASE_CONTROL_DATA_PUSH_RES 0x4008 //控制数据推送响应
#define TESTCASE_DELETE_ONE_REQ 0x4009  // 删除单个测试用例请求
#define TESTCASE_DELETE_ONE_RES 0x4010  // 删除单个测试用例响应

/* server端与GUI端交互消息定义*/
#define MSG_CONFIG 0X10001              // 参数配置下发  GUI->Server
#define MSG_DATA_CAPTURE_UPDATE 0X10002 // 采集状态更新  Server->GUI
#define MSG_DATA_FREQ_UPDATE 0X10003    // 数据频率定时上报  Server->GUI
#define MSG_DATA_QUERY_REQ 0X10004      // 数据查询请求  GUI->Server
#define MSG_DATA_QUERY_RES 0X10005      // 数据查询响应  Server->GUI
#define MSG_DATA_COMPRESS_REQ 0X10006 // 数据压缩打包命令下发  GUI->Server
#define MSG_DATA_COMPRESS_RES 0X10007 // 数据压缩打包命令返回  Server->GUI
#define MSG_SYSTEM_START 0X10008      // 开始采集  GUI->Server
#define MSG_SYSTEM_PAUSE 0X10009      // 暂停采集  GUI->Server
#define MSG_SYSTEM_STOP 0X10010       // 停止采集  GUI->Server
#define MSG_CONFIG_REQ 0X10011        // 参数配置请求  GUI->Server
#define MSG_CONFIG_RES 0X10012        // 参数配置响应  Server->GUI
#define MSG_ROADINFO_UPDATE 0X10013 // 路口信息更新  Server->GUI
#define MSG_SYSTEM_INFO_REQ 0X10014 // 系统信息请求  GUI->Server
#define MSG_SYSTEM_INFO_RES 0X10015 // 系统信息请求回应  Server->GUI
#define MSG_CLIENT_INFO_REQ 0X10016 // 客户端信息请求   GUI->Server
#define MSG_CLIENT_INFO_RES 0X10017 // 客户端信息请求回应   Server->GUI
#endif